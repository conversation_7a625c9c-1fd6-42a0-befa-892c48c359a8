from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException
from core.security import (
    create_access_token,
    create_refresh_token,
    verify_password,
    get_tenant_info,
    verify_refresh_token,
    store_refresh_token,
    revoke_refresh_token
)
from core.database import get_tenant_id_and_name_from_slug, get_async_db_from_tenant_id
from core.helper.mongo_helper import convert_objectid_to_str
from models.security import (
    OAuth2PasswordRequestFormWithClientID,
    RefreshTokenRequest,
    TokenResponse,
    RefreshTokenResponse
)
from models.user import UserTenantDB
from datetime import timedelta, datetime
import logging
import jwt

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Authentication"])

@router.post("/login", response_model=TokenResponse)
async def login(form_data: OAuth2PasswordRequestFormWithClientID = Depends()):
    """
    Login endpoint that authenticates users and returns JWT access and refresh tokens
    """
    # Find the database name of that tenant
    try:
        result = get_tenant_id_and_name_from_slug(form_data.client_id)

        tenant_id = str(result["_id"])
        tenant_database = get_async_db_from_tenant_id(tenant_id)

        # Connect to the user_collection of that database
        user = await tenant_database.users.find_one({"username": form_data.username})

        if not user:
            logger.error(f"User not found: {form_data.username}")
            raise HTTPException(status_code=401, detail="User not found")

        if not verify_password(form_data.password, user["hashed_password"]):
            logger.error(f"Incorrect credentials: {form_data.username}")
            raise HTTPException(status_code=401, detail="Incorrect credentials")

        # Create access token with shorter expiration (15 minutes)
        access_token_expires = timedelta(minutes=15)
        access_token = create_access_token(
            data={"sub": user["username"], "role": user["role"], "tenant_id": tenant_id},
            expires_delta=access_token_expires
        )

        # Create refresh token with longer expiration (7 days)
        refresh_token_expires = timedelta(days=7)
        refresh_token = create_refresh_token(
            data={"sub": user["username"], "role": user["role"], "tenant_id": tenant_id},
            expires_delta=refresh_token_expires
        )

        # Extract JTI from refresh token for storage
        refresh_payload = jwt.decode(refresh_token, options={"verify_signature": False})
        jti = refresh_payload.get("jti")

        # Store refresh token in database
        refresh_expires_at = datetime.utcnow() + refresh_token_expires
        await store_refresh_token(tenant_id, user["username"], jti, refresh_expires_at)

        # Convert ObjectId to string for JSON response
        user = convert_objectid_to_str(user)

        logger.info(f"User logged in: {user['username']} for tenant: {result['name']}")

        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=int(access_token_expires.total_seconds())
        )
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@router.post("/refresh", response_model=RefreshTokenResponse)
async def refresh_access_token(request: RefreshTokenRequest):
    """
    Refresh access token using a valid refresh token
    """
    try:
        # Get tenant information from client_id
        result = get_tenant_id_and_name_from_slug(request.client_id)
        tenant_id = str(result["_id"])

        # Verify refresh token and get user data
        user, jti = await verify_refresh_token(request.refresh_token, tenant_id)

        # Create new access token
        access_token_expires = timedelta(minutes=15)
        access_token = create_access_token(
            data={"sub": user["username"], "role": user["role"], "tenant_id": tenant_id},
            expires_delta=access_token_expires
        )

        logger.info(f"Access token refreshed for user: {user['username']}")

        return RefreshTokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=int(access_token_expires.total_seconds())
        )

    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise HTTPException(status_code=401, detail="Invalid refresh token")


@router.post("/logout")
async def logout(request: RefreshTokenRequest):
    """
    Logout user by revoking the refresh token
    """
    try:
        # Get tenant information from client_id
        result = get_tenant_id_and_name_from_slug(request.client_id)
        tenant_id = str(result["_id"])

        # Extract JTI from refresh token
        refresh_payload = jwt.decode(request.refresh_token, options={"verify_signature": False})
        jti = refresh_payload.get("jti")

        if jti:
            # Revoke the refresh token
            await revoke_refresh_token(tenant_id, jti)
            logger.info(f"User logged out, refresh token revoked: {jti}")

        return {"message": "Successfully logged out"}

    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        # Even if there's an error, we'll return success for security
        return {"message": "Successfully logged out"}


@router.get("/verify-token")
async def verify_token(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
    """
    Verify if the provided token is valid and return user information
    """
    return {
        "valid": True,
        "user": user_tenant_info.user.model_dump(),
        "tenant_id": user_tenant_info.tenant_id
    }